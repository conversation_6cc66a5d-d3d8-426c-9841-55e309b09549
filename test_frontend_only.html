<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dots and Boxes - Frontend Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #000;
            position: absolute;
            transform: translate(-50%, -50%);
            cursor: pointer;
            z-index: 10;
            transition: background-color 0.2s, transform 0.2s;
        }

        .dot:hover {
            background-color: #333;
            transform: translate(-50%, -50%) scale(1.2);
        }

        .line {
            position: absolute;
            z-index: 5;
        }

        .line.player1 {
            background-color: #ef4444;
        }

        .line.player2 {
            background-color: #3b82f6;
        }

        .horizontal-line {
            height: 3px;
            width: 60px;
            transform: translateY(-50%);
        }

        .vertical-line {
            width: 3px;
            height: 60px;
            transform: translateX(-50%);
        }

        .box {
            position: absolute;
            width: 57px;
            height: 57px;
            border: none;
            transition: background-color 0.3s;
            border-radius: 4px;
        }

        .box.player1 {
            background-color: rgba(239, 68, 68, 0.7);
        }

        .box.player2 {
            background-color: rgba(59, 130, 246, 0.7);
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen flex items-center justify-center p-4">
    <div class="text-center">
        <h1 class="text-4xl font-bold mb-8">🎮 Dots and Boxes - Frontend Test</h1>
        
        <div class="bg-gray-800 rounded-lg p-8 mb-8">
            <h2 class="text-2xl font-bold mb-4">Game Board Preview</h2>
            <div id="gameBoard" class="relative bg-gray-50 border-2 border-gray-200 rounded-lg mx-auto" style="width: 480px; height: 480px;">
                <!-- Game elements will be generated here -->
            </div>
        </div>
        
        <div class="bg-gray-800 rounded-lg p-6 max-w-2xl">
            <h2 class="text-xl font-bold mb-4">🔧 Setup Instructions</h2>
            <div class="text-left space-y-4">
                <div class="bg-gray-700 p-4 rounded">
                    <h3 class="font-bold text-yellow-400">Step 1: Install Go</h3>
                    <p>Download and install Go from: <a href="https://golang.org/dl/" class="text-blue-400 underline" target="_blank">https://golang.org/dl/</a></p>
                    <p class="text-sm text-gray-300">Choose the installer for your operating system (Windows, macOS, or Linux)</p>
                </div>
                
                <div class="bg-gray-700 p-4 rounded">
                    <h3 class="font-bold text-yellow-400">Step 2: Verify Installation</h3>
                    <p>Open a terminal/command prompt and run:</p>
                    <code class="bg-black p-2 rounded block mt-2">go version</code>
                    <p class="text-sm text-gray-300">You should see something like "go version go1.21.0 ..."</p>
                </div>
                
                <div class="bg-gray-700 p-4 rounded">
                    <h3 class="font-bold text-yellow-400">Step 3: Start the Server</h3>
                    <p><strong>Windows:</strong> Double-click <code>start_local_server.bat</code></p>
                    <p><strong>macOS/Linux:</strong> Run <code>./start_local_server.sh</code> in terminal</p>
                    <p><strong>Manual:</strong> Run <code>go run local_server.go</code></p>
                </div>
                
                <div class="bg-gray-700 p-4 rounded">
                    <h3 class="font-bold text-yellow-400">Step 4: Test Multiplayer</h3>
                    <p>Open <code>http://localhost:8080</code> in two browser tabs</p>
                    <p class="text-sm text-gray-300">Both players should connect and be able to play together</p>
                </div>
            </div>
        </div>
        
        <div class="mt-8 text-gray-400">
            <p>This is a frontend-only preview. The dots above show the game board layout.</p>
            <p>Follow the setup instructions to run the full multiplayer game.</p>
        </div>
    </div>

    <script>
        // Simple frontend preview - just show the dots
        const gameBoard = document.getElementById('gameBoard');
        const gridSize = 8;
        const spacing = 60;
        const offset = 30;

        // Create dots for preview
        for (let row = 0; row < gridSize; row++) {
            for (let col = 0; col < gridSize; col++) {
                const dot = document.createElement('div');
                dot.className = 'dot';
                dot.style.left = `${offset + col * spacing}px`;
                dot.style.top = `${offset + row * spacing}px`;
                gameBoard.appendChild(dot);
            }
        }

        // Add some sample lines and boxes for preview
        setTimeout(() => {
            // Add a few sample lines
            const sampleLines = [
                {type: 'h', row: 1, col: 1, player: '1'},
                {type: 'v', row: 1, col: 1, player: '1'},
                {type: 'h', row: 2, col: 1, player: '2'},
                {type: 'v', row: 1, col: 2, player: '2'},
            ];

            sampleLines.forEach(lineData => {
                const line = document.createElement('div');
                line.className = `line player${lineData.player}`;
                
                if (lineData.type === 'h') {
                    line.classList.add('horizontal-line');
                    line.style.left = `${offset + lineData.col * spacing + 8}px`;
                    line.style.top = `${offset + lineData.row * spacing}px`;
                } else {
                    line.classList.add('vertical-line');
                    line.style.left = `${offset + lineData.col * spacing}px`;
                    line.style.top = `${offset + lineData.row * spacing + 8}px`;
                }
                
                gameBoard.appendChild(line);
            });

            // Add a sample completed box
            const box = document.createElement('div');
            box.className = 'box player1';
            box.style.left = `${offset + 1 * spacing + 1.5}px`;
            box.style.top = `${offset + 1 * spacing + 1.5}px`;
            gameBoard.appendChild(box);
        }, 1000);
    </script>
</body>
</html>
