# 🎮 Dots and Boxes - Local Testing Setup

This guide will help you run the Dots and Boxes multiplayer game locally for testing purposes.

## 🎯 Quick Preview

If you want to see the game interface before setting up the server:
1. Open `test_frontend_only.html` in your browser
2. This shows the game board layout and setup instructions

## 📋 Prerequisites

### 1. Go Programming Language (Required)
**Download and Install:**
- **Windows**: Download the `.msi` installer from https://golang.org/dl/
- **macOS**: Download the `.pkg` installer or use Homebrew: `brew install go`
- **Linux**: Download the `.tar.gz` or use your package manager: `sudo apt install golang-go`

**Verify Installation:**
Open a terminal/command prompt and run:
```bash
go version
```
You should see output like: `go version go1.21.0 windows/amd64`

### 2. Web Browser
- Chrome, Firefox, Safari, or Edge
- You'll need to open multiple tabs to test multiplayer functionality

## 🚀 Quick Start

### Method 1: Use the Startup Scripts (Easiest)

**Windows:**
1. Double-click `start_local_server.bat`
2. A command window will open and start the server

**macOS/Linux:**
1. Open Terminal in the project folder
2. Run: `./start_local_server.sh`

### Method 2: Manual Setup

**Step 1: Install Dependencies**
```bash
go mod tidy
```

**Step 2: Start the Local Server**
```bash
go run local_server.go
```

**Expected Output:**
```
🎮 Dots and Boxes Local Server
📡 Server starting on http://localhost:8080
🌐 Open http://localhost:8080 in two browser tabs to test multiplayer
🔧 Press Ctrl+C to stop the server
```

### 3. Test the Game

1. **Open your web browser** and navigate to: `http://localhost:8080`
2. **Open a second tab** (or use a different browser/incognito mode) and navigate to the same URL
3. **Both players should connect** and see the loading screen, then the game board
4. **Take turns drawing lines** between dots by clicking and dragging
5. **Complete boxes** to score points and get extra turns
6. **Win the game** by completing more boxes than your opponent

## 🎯 Testing Features

### ✅ What You Can Test:
- **Multiplayer Connection**: Two players joining the same game
- **Real-time Synchronization**: Moves appearing instantly on both screens
- **Game Mechanics**: Line drawing, box completion, scoring
- **Turn Management**: Players taking turns, extra turns for completing boxes
- **Sound Effects**: Audio feedback for moves and game events
- **Game End**: Win/lose/draw scenarios
- **Connection Status**: Visual indicators for connection state
- **Player Disconnection**: What happens when a player leaves

### 🔧 Local Testing Limitations:
- **No User Authentication**: Players are automatically assigned as "Player 1" and "Player 2"
- **Single Room**: All connections join the same game room
- **No Persistence**: Game state is lost when server restarts
- **No Lobby System**: Games start immediately when 2 players connect

## 🐛 Troubleshooting

### Server Won't Start
- **Check if port 8080 is available**: `netstat -an | grep 8080`
- **Try a different port**: Modify the port in `local_server.go` line with `:8080`

### WebSocket Connection Issues
- **Check browser console** for error messages (F12 → Console tab)
- **Disable browser extensions** that might block WebSocket connections
- **Try incognito/private browsing mode**

### Game Not Loading
- **Refresh the page** (F5 or Ctrl+R)
- **Clear browser cache** (Ctrl+Shift+Delete)
- **Check server logs** in the terminal where you ran the server

### Players Can't Connect
- **Maximum 2 players**: The local server only supports 2 concurrent players
- **Restart the server**: Stop (Ctrl+C) and restart the server
- **Use different browsers**: Try Chrome + Firefox, or use incognito mode

## 📁 File Structure

```
dots-and-boxes/
├── local_server.go          # Local testing server
├── go.mod                   # Go module dependencies
├── index.html               # Game frontend
├── server.go                # Original GameyDay server (not used for local testing)
└── README_LOCAL_TESTING.md  # This file
```

## 🔄 Making Changes

### Frontend Changes (index.html)
- **Edit and save** the file
- **Refresh browser tabs** to see changes
- **No server restart needed**

### Backend Changes (local_server.go)
- **Stop the server** (Ctrl+C)
- **Save your changes**
- **Restart the server**: `go run local_server.go`
- **Refresh browser tabs**

## 🎮 Game Controls

- **Draw Lines**: Click and drag between adjacent dots
- **Invalid Moves**: Red error sound for invalid line attempts
- **Box Completion**: Pleasant chord sound when completing a box
- **Turn Indicator**: Shows whose turn it is
- **Score Display**: Real-time score updates
- **Connection Status**: Green dot = connected, Red dot = disconnected

## 🏁 Next Steps

Once you've tested locally and everything works:
1. **Deploy to GameyDay platform** using the original `server.go`
2. **Test with real users** in the GameyDay environment
3. **Monitor performance** and fix any issues that arise

Happy testing! 🎉
