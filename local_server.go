package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// Local game state management
type LocalPlayerInfo struct {
	Username string
	Symbol   string
	Conn     *websocket.Conn
	Active   bool
}

type LocalGameRoom struct {
	ID          string
	Players     map[string]*LocalPlayerInfo
	GameState   *LocalDotsAndBoxes
	mu          sync.Mutex
	GameStarted bool
}

type LocalDotsAndBoxes struct {
	GridSize    int                `json:"gridSize"`
	Lines       map[string]string  `json:"lines"`
	Boxes       map[string]string  `json:"boxes"`
	CurrentTurn string             `json:"currentTurn"`
	Scores      map[string]int     `json:"scores"`
	IsGameOver  bool               `json:"isGameOver"`
	Winner      string             `json:"winner"`
	Players     map[string]string  `json:"players"`
}

var (
	upgrader = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // Allow all origins for local testing
		},
	}
	
	gameRoom = &LocalGameRoom{
		ID:      "local-room",
		Players: make(map[string]*LocalPlayerInfo),
		GameState: &LocalDotsAndBoxes{
			GridSize:    8,
			Lines:       make(map[string]string),
			Boxes:       make(map[string]string),
			CurrentTurn: "",
			Scores:      map[string]int{"1": 0, "2": 0},
			IsGameOver:  false,
			Winner:      "",
			Players:     make(map[string]string),
		},
	}
)

func main() {
	// Serve static files
	http.Handle("/", http.FileServer(http.Dir(".")))
	
	// Game endpoints
	http.HandleFunc("/game-play/room-service", handleRoomService)
	http.HandleFunc("/game-play/get-return-url", handleReturnUrl)
	http.HandleFunc("/ws", handleWebSocket)
	
	fmt.Println("🎮 Dots and Boxes Local Server")
	fmt.Println("📡 Server starting on http://localhost:8080")
	fmt.Println("🌐 Open http://localhost:8080 in two browser tabs to test multiplayer")
	fmt.Println("🔧 Press Ctrl+C to stop the server")
	
	log.Fatal(http.ListenAndServe(":8080", nil))
}

func handleRoomService(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	response := map[string]string{
		"wsURL": "ws://localhost:8080/ws",
	}
	json.NewEncoder(w).Encode(response)
}

func handleReturnUrl(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	response := map[string]string{
		"returnUrl": "http://localhost:8080",
	}
	json.NewEncoder(w).Encode(response)
}

func handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}
	defer conn.Close()
	
	playerID := fmt.Sprintf("player_%d", time.Now().UnixNano())
	log.Printf("New player connected: %s", playerID)
	
	// Add player to room
	gameRoom.mu.Lock()
	if len(gameRoom.Players) >= 2 {
		gameRoom.mu.Unlock()
		conn.WriteJSON(map[string]interface{}{
			"type":    "error",
			"message": "Game room is full",
		})
		return
	}
	
	symbol := "1"
	if len(gameRoom.Players) == 1 {
		symbol = "2"
	}
	
	player := &LocalPlayerInfo{
		Username: fmt.Sprintf("Player %s", symbol),
		Symbol:   symbol,
		Conn:     conn,
		Active:   true,
	}
	
	gameRoom.Players[playerID] = player
	gameRoom.GameState.Players[symbol] = player.Username
	
	// Start game if we have 2 players
	if len(gameRoom.Players) == 2 && !gameRoom.GameStarted {
		gameRoom.GameState.CurrentTurn = "1"
		gameRoom.GameStarted = true
		
		// Notify all players that game is starting
		for _, p := range gameRoom.Players {
			if p.Active {
				p.Conn.WriteJSON(map[string]interface{}{
					"type":  "game_start",
					"state": gameRoom.GameState,
				})
			}
		}
	}
	gameRoom.mu.Unlock()
	
	// Send initial state to the new player
	conn.WriteJSON(map[string]interface{}{
		"type": "personal_state",
		"state": map[string]interface{}{
			"symbol":      symbol,
			"gridSize":    gameRoom.GameState.GridSize,
			"lines":       gameRoom.GameState.Lines,
			"boxes":       gameRoom.GameState.Boxes,
			"currentTurn": gameRoom.GameState.CurrentTurn,
			"players":     gameRoom.GameState.Players,
			"scores":      gameRoom.GameState.Scores,
			"isGameOver":  gameRoom.GameState.IsGameOver,
			"winner":      gameRoom.GameState.Winner,
		},
	})
	
	// Handle messages from this player
	for {
		var message map[string]interface{}
		err := conn.ReadJSON(&message)
		if err != nil {
			log.Printf("Player %s disconnected: %v", playerID, err)
			break
		}
		
		handlePlayerMessage(playerID, message)
	}
	
	// Clean up when player disconnects
	gameRoom.mu.Lock()
	if player, exists := gameRoom.Players[playerID]; exists {
		player.Active = false
		
		// End game if a player leaves
		if gameRoom.GameStarted && !gameRoom.GameState.IsGameOver {
			gameRoom.GameState.IsGameOver = true
			
			// Find remaining player and make them winner
			for _, p := range gameRoom.Players {
				if p.Active {
					gameRoom.GameState.Winner = p.Symbol
					break
				}
			}
			
			// Notify remaining players
			for _, p := range gameRoom.Players {
				if p.Active {
					p.Conn.WriteJSON(map[string]interface{}{
						"type":   "game_over",
						"winner": gameRoom.GameState.Winner,
						"state":  gameRoom.GameState,
					})
				}
			}
		}
	}
	gameRoom.mu.Unlock()
}

func handlePlayerMessage(playerID string, message map[string]interface{}) {
	gameRoom.mu.Lock()
	defer gameRoom.mu.Unlock()

	player, exists := gameRoom.Players[playerID]
	if !exists || !player.Active {
		return
	}

	msgType, ok := message["type"].(string)
	if !ok {
		return
	}

	switch msgType {
	case "ping":
		player.Conn.WriteJSON(map[string]interface{}{"type": "pong"})

	case "game_action":
		if gameRoom.GameState.IsGameOver {
			return
		}

		if gameRoom.GameState.CurrentTurn != player.Symbol {
			return
		}

		action, ok := message["action"].(map[string]interface{})
		if !ok {
			return
		}

		startRow, _ := action["startRow"].(float64)
		startCol, _ := action["startCol"].(float64)
		endRow, _ := action["endRow"].(float64)
		endCol, _ := action["endCol"].(float64)

		// Validate move
		if !isValidLine(int(startRow), int(startCol), int(endRow), int(endCol)) {
			return
		}

		lineID := getLineID(int(startRow), int(startCol), int(endRow), int(endCol))
		if _, exists := gameRoom.GameState.Lines[lineID]; exists {
			return
		}

		// Make the move
		gameRoom.GameState.Lines[lineID] = player.Symbol

		// Check for completed boxes
		completedBoxes := checkCompletedBoxes()

		if len(completedBoxes) > 0 {
			// Player gets points and another turn
			gameRoom.GameState.Scores[player.Symbol] += len(completedBoxes)
			for _, boxID := range completedBoxes {
				gameRoom.GameState.Boxes[boxID] = player.Symbol
			}
			// Current player keeps their turn
		} else {
			// Switch turns
			if gameRoom.GameState.CurrentTurn == "1" {
				gameRoom.GameState.CurrentTurn = "2"
			} else {
				gameRoom.GameState.CurrentTurn = "1"
			}
		}

		// Check if game is over
		totalBoxes := (gameRoom.GameState.GridSize - 1) * (gameRoom.GameState.GridSize - 1)
		if len(gameRoom.GameState.Boxes) == totalBoxes {
			gameRoom.GameState.IsGameOver = true
			if gameRoom.GameState.Scores["1"] > gameRoom.GameState.Scores["2"] {
				gameRoom.GameState.Winner = "1"
			} else if gameRoom.GameState.Scores["2"] > gameRoom.GameState.Scores["1"] {
				gameRoom.GameState.Winner = "2"
			}
			// If scores are equal, winner remains empty (draw)
		}

		// Broadcast game state to all players
		for _, p := range gameRoom.Players {
			if p.Active {
				if gameRoom.GameState.IsGameOver {
					p.Conn.WriteJSON(map[string]interface{}{
						"type":   "game_over",
						"winner": gameRoom.GameState.Winner,
						"state":  gameRoom.GameState,
					})
				} else {
					p.Conn.WriteJSON(map[string]interface{}{
						"type":  "game_update",
						"state": gameRoom.GameState,
					})
				}
			}
		}
	}
}

func isValidLine(sr, sc, er, ec int) bool {
	rowDiff := abs(sr - er)
	colDiff := abs(sc - ec)

	// Must be adjacent (horizontally or vertically, not diagonally)
	return (rowDiff == 1 && colDiff == 0) || (rowDiff == 0 && colDiff == 1)
}

func getLineID(sr, sc, er, ec int) string {
	// Normalize line ID to ensure consistency
	minRow, maxRow := min(sr, er), max(sr, er)
	minCol, maxCol := min(sc, ec), max(sc, ec)

	if minRow == maxRow {
		// Horizontal line
		return fmt.Sprintf("h-%d-%d", minRow, minCol)
	} else {
		// Vertical line
		return fmt.Sprintf("v-%d-%d", minRow, minCol)
	}
}

func checkCompletedBoxes() []string {
	var completedBoxes []string

	// Check all possible boxes that could be completed
	for row := 0; row < gameRoom.GameState.GridSize-1; row++ {
		for col := 0; col < gameRoom.GameState.GridSize-1; col++ {
			boxID := fmt.Sprintf("box-%d-%d", row, col)

			// Skip if box is already completed
			if _, exists := gameRoom.GameState.Boxes[boxID]; exists {
				continue
			}

			// Check if all four sides of the box are drawn
			top := fmt.Sprintf("h-%d-%d", row, col)
			bottom := fmt.Sprintf("h-%d-%d", row+1, col)
			left := fmt.Sprintf("v-%d-%d", row, col)
			right := fmt.Sprintf("v-%d-%d", row, col+1)

			if hasLine(top) && hasLine(bottom) && hasLine(left) && hasLine(right) {
				completedBoxes = append(completedBoxes, boxID)
			}
		}
	}

	return completedBoxes
}

func hasLine(lineID string) bool {
	_, exists := gameRoom.GameState.Lines[lineID]
	return exists
}

func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
